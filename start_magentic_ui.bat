@echo off
echo ========================================
echo    Magentic UI Startup Script
echo ========================================
echo.

REM Wechsle zum Magentic UI Verzeichnis
cd /d "C:\Users\<USER>\Downloads\AI\magentic-ui"

echo Aktiviere Python Virtual Environment...
call .venv\Scripts\activate.bat

echo.
echo Setze OpenRouter API Key...
set OPENAI_API_KEY=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

echo.
echo Starte Magentic UI Server...
echo Konfiguration: OpenRouter mit google/gemini-2.0-flash-exp
echo Port: 8081
echo.

REM Starte Magentic UI im Hintergrund
start /B python -m magentic_ui ui --config config.yaml --port 8081

echo Warte 15 Sekunden bis der Server gestartet ist...
timeout /t 15 /nobreak >nul

echo.
echo Öffne Browser mit Magentic UI...
start http://localhost:8081

echo.
echo ========================================
echo Magentic UI läuft jetzt!
echo Browser sollte sich automatisch öffnen
echo URL: http://localhost:8081
echo.
echo Zum Beenden: Strg+C drücken
echo ========================================
echo.

REM Halte das Fenster offen
pause
