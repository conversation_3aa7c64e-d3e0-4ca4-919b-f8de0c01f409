# config.yaml

######################################
# OpenRouter model configuration     #
######################################
model_config: &client
  provider: autogen_ext.models.openai.OpenAIChatCompletionClient
  config:
    model: google/gemini-2.0-flash-exp
    api_key: 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
    base_url: https://openrouter.ai/api/v1
    max_retries: 10

##########################
# Clients for each agent #
##########################
orchestrator_client: *client
coder_client: *client
web_surfer_client: *client
file_surfer_client: *client
action_guard_client: *client
